'use client';

import { useEffect, useRef, useState } from 'react';
import { io, Socket } from 'socket.io-client';
import { Fixture } from '@/lib/api';

interface UseSocketOptions {
  namespace?: string;
  autoConnect?: boolean;
}

// Event data types
type GoalEventData = { fixtureId: number; team: 'home' | 'away'; player: string; minute: number };
type CardEventData = { fixtureId: number; team: 'home' | 'away'; player: string; card: 'yellow' | 'red'; minute: number };
type StatusEventData = { fixtureId: number; status: string; elapsed?: number };

// Event types for internal use
type FixtureEvent = { type: 'goal' | 'card' | 'status'; timestamp: number } & (GoalEventData | CardEventData | StatusEventData);

export function useSocket(options: UseSocketOptions = {}) {
  const { namespace = '/fixtures', autoConnect = true } = options;
  const [socket, setSocket] = useState<Socket | null>(null);
  const [connected, setConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const socketRef = useRef<Socket | null>(null);

  useEffect(() => {
    if (!autoConnect) return;

    const SOCKET_URL = process.env.NEXT_PUBLIC_SOCKET_URL || 'http://localhost:3000';
    
    // Create socket connection
    const newSocket = io(`${SOCKET_URL}${namespace}`, {
      transports: ['websocket', 'polling'],
      timeout: 20000,
      forceNew: true,
    });

    socketRef.current = newSocket;
    setSocket(newSocket);

    // Connection event handlers
    newSocket.on('connect', () => {
      console.log(`Connected to ${namespace} namespace`);
      setConnected(true);
      setError(null);
    });

    newSocket.on('disconnect', (reason) => {
      console.log(`Disconnected from ${namespace} namespace:`, reason);
      setConnected(false);
    });

    newSocket.on('connect_error', (err) => {
      console.error(`Connection error to ${namespace} namespace:`, err);
      setError(err.message);
      setConnected(false);
    });

    // Cleanup on unmount
    return () => {
      if (newSocket) {
        newSocket.disconnect();
      }
    };
  }, [namespace, autoConnect]);

  // Subscribe to fixture updates
  const subscribeToFixture = (fixtureId: number) => {
    if (socket && connected) {
      socket.emit('subscribe-fixture', fixtureId);
      console.log(`Subscribed to fixture ${fixtureId}`);
    }
  };

  // Unsubscribe from fixture updates
  const unsubscribeFromFixture = (fixtureId: number) => {
    if (socket && connected) {
      socket.emit('unsubscribe-fixture', fixtureId);
      console.log(`Unsubscribed from fixture ${fixtureId}`);
    }
  };

  // Subscribe to league updates
  const subscribeToLeague = (leagueId: number) => {
    if (socket && connected) {
      socket.emit('subscribe-league', leagueId);
      console.log(`Subscribed to league ${leagueId}`);
    }
  };

  // Unsubscribe from league updates
  const unsubscribeFromLeague = (leagueId: number) => {
    if (socket && connected) {
      socket.emit('unsubscribe-league', leagueId);
      console.log(`Unsubscribed from league ${leagueId}`);
    }
  };

  // Subscribe to live fixtures
  const subscribeToLiveFixtures = () => {
    if (socket && connected) {
      socket.emit('subscribe-live');
      console.log('Subscribed to live fixtures');
    }
  };

  // Unsubscribe from live fixtures
  const unsubscribeFromLiveFixtures = () => {
    if (socket && connected) {
      socket.emit('unsubscribe-live');
      console.log('Unsubscribed from live fixtures');
    }
  };

  // Generic event listener
  const on = (event: string, callback: (...args: unknown[]) => void) => {
    if (socket) {
      socket.on(event, callback);
    }
  };

  // Remove event listener
  const off = (event: string, callback?: (...args: unknown[]) => void) => {
    if (socket) {
      if (callback) {
        socket.off(event, callback);
      } else {
        socket.off(event);
      }
    }
  };

  // Emit event
  const emit = (event: string, data?: unknown) => {
    if (socket && connected) {
      socket.emit(event, data);
    }
  };

  return {
    socket,
    connected,
    error,
    subscribeToFixture,
    unsubscribeFromFixture,
    subscribeToLeague,
    unsubscribeFromLeague,
    subscribeToLiveFixtures,
    unsubscribeFromLiveFixtures,
    on,
    off,
    emit,
  };
}

// Hook for fixture-specific updates
export function useFixtureSocket(fixtureId: number | null) {
  const { 
    connected, 
    subscribeToFixture, 
    unsubscribeFromFixture, 
    on, 
    off 
  } = useSocket();
  
  const [fixture, setFixture] = useState<Fixture | null>(null);
  const [events, setEvents] = useState<FixtureEvent[]>([]);

  useEffect(() => {
    if (!fixtureId || !connected) return;

    // Subscribe to fixture updates
    subscribeToFixture(fixtureId);

    // Set up event listeners
    const handleFixtureUpdate = (...args: unknown[]) => {
      const updatedFixture = args[0] as Fixture;
      if (updatedFixture.id === fixtureId) {
        setFixture(updatedFixture);
      }
    };

    const handleGoal = (...args: unknown[]) => {
      const data = args[0] as GoalEventData;
      if (data.fixtureId === fixtureId) {
        setEvents(prev => [...prev, { type: 'goal', ...data, timestamp: Date.now() }]);
      }
    };

    const handleCard = (...args: unknown[]) => {
      const data = args[0] as CardEventData;
      if (data.fixtureId === fixtureId) {
        setEvents(prev => [...prev, { type: 'card', ...data, timestamp: Date.now() }]);
      }
    };

    const handleStatus = (...args: unknown[]) => {
      const data = args[0] as StatusEventData;
      if (data.fixtureId === fixtureId) {
        setEvents(prev => [...prev, { type: 'status', ...data, timestamp: Date.now() }]);
      }
    };

    on('fixture-update', handleFixtureUpdate);
    on('fixture-goal', handleGoal);
    on('fixture-card', handleCard);
    on('fixture-status', handleStatus);

    // Cleanup
    return () => {
      unsubscribeFromFixture(fixtureId);
      off('fixture-update', handleFixtureUpdate);
      off('fixture-goal', handleGoal);
      off('fixture-card', handleCard);
      off('fixture-status', handleStatus);
    };
  }, [fixtureId, connected, subscribeToFixture, unsubscribeFromFixture, on, off]);

  return {
    fixture,
    events,
    connected,
  };
}

// Hook for live fixtures
export function useLiveFixtures() {
  const { 
    connected, 
    subscribeToLiveFixtures, 
    unsubscribeFromLiveFixtures, 
    on, 
    off 
  } = useSocket();
  
  const [liveFixtures, setLiveFixtures] = useState<Fixture[]>([]);

  useEffect(() => {
    if (!connected) return;

    // Subscribe to live fixtures
    subscribeToLiveFixtures();

    // Set up event listener
    const handleLiveFixtures = (...args: unknown[]) => {
      const fixtures = args[0] as Fixture[];
      setLiveFixtures(fixtures);
    };

    on('live-fixtures', handleLiveFixtures);

    // Cleanup
    return () => {
      unsubscribeFromLiveFixtures();
      off('live-fixtures', handleLiveFixtures);
    };
  }, [connected, subscribeToLiveFixtures, unsubscribeFromLiveFixtures, on, off]);

  return {
    liveFixtures,
    connected,
  };
}
