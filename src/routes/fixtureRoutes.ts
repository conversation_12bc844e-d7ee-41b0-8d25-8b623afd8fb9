import express, { Router, RequestHandler } from 'express';
import { getFixturesCollection, Fixture } from '../models/Fixture';
import {
    fetchHeadToHead,
    fetchFixtureStatistics,
    fetchFixtureEvents,
    fetchFixtureLineups,
    fetchFixturePlayers,
    fetchFixtureRounds,
    fetchLeagues
} from '../services/apiFootball'; // Import service functions
import { getRedisClient } from '../config/redis';
import { Filter, Sort } from 'mongodb';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { applyStatusMapping, applyStatusMappingToArray } from '../utils/statusMapping';
import { getLeaguesCollection } from '../models/League';

// Extend dayjs with timezone support
dayjs.extend(utc);
dayjs.extend(timezone);

const router: Router = express.Router();

// Helper function to get the current season for a league
async function getCurrentSeasonForLeague(leagueId: number): Promise<number> {
    try {
        // Get from database only (no external API calls for performance)
        const leaguesCollection = getLeaguesCollection();
        const league = await leaguesCollection.findOne({ _id: leagueId });

        if (league && league.seasons) {
            // Find the current season
            const currentSeason = league.seasons.find(season => season.current);

            if (currentSeason) {
                console.log(`Found current season ${currentSeason.year} for league ID ${leagueId} from database`);
                return currentSeason.year;
            }

            // If no current season is found, use the most recent season
            const sortedSeasons = [...league.seasons].sort((a, b) => b.year - a.year);
            if (sortedSeasons.length > 0) {
                console.log(`No current season found for league ID ${leagueId}. Using most recent season ${sortedSeasons[0].year} from database.`);
                return sortedSeasons[0].year;
            }
        }

        // If not found in database, use a reasonable default
        console.log(`League ${leagueId} not found in database. Using default season 2025.`);
        return 2025; // Default to 2025 as it's the current season for most leagues
    } catch (error) {
        console.error(`Error fetching current season for league ID ${leagueId}:`, error);
        console.log(`Using default season 2025 for league ID ${leagueId}.`);
        return 2025; // Default to 2025 in case of error
    }
}
const FIXTURES_BASE_CACHE_KEY = 'fixtures:';
const H2H_BASE_CACHE_KEY = 'fixtures:h2h:';
const FIXTURE_STATS_CACHE_KEY = 'fixtures:stats:';
const FIXTURE_EVENTS_CACHE_KEY = 'fixtures:events:';
const FIXTURE_LINEUPS_CACHE_KEY = 'fixtures:lineups:';
const FIXTURE_PLAYERS_CACHE_KEY = 'fixtures:players:';
const FIXTURE_ROUNDS_CACHE_KEY = 'fixtures:rounds:';
// Cache durations
const CACHE_TTL_5_SECONDS = 5;
const CACHE_TTL_15_SECONDS = 15;
const CACHE_TTL_MINUTE = 60;
const CACHE_TTL_5_MINUTES = CACHE_TTL_MINUTE * 5;
const CACHE_TTL_HOUR = 60 * 60;
const CACHE_TTL_DAY = 60 * 60 * 24;

// Define the handler function with RequestHandler type
const getFixturesHandler: RequestHandler = async (req, res) => {
    const redisClient = getRedisClient();
    const queryParams = req.query;

    // --- Parameter Validation and Processing ---
    const {
        id, ids, live, date, league, season, team,
        last, next, from, to, round, status, venue,
        page, pageSize, timezone // Add timezone parameter
    } = queryParams;
    // Note: timezone parameter affects date filtering logic

    // Basic validation (more robust validation can be added)
    const fixtureId = id ? parseInt(id as string) : undefined;
    const leagueId = league ? parseInt(league as string) : undefined;
    const seasonYear = season ? parseInt(season as string) : undefined;
    const teamIdNum = team ? parseInt(team as string) : undefined; // Renamed parsed variable
    const venueId = venue ? parseInt(venue as string) : undefined;
    const lastN = last ? parseInt(last as string) : undefined;
    const nextN = next ? parseInt(next as string) : undefined;

    // --- Build MongoDB Filter ---
    const filter: Filter<Fixture> = {};
    // Add a secondary sort by _id to ensure consistent ordering and prevent duplicates during pagination
    let sort: Sort = { 'fixture.timestamp': 1, '_id': 1 }; // Default sort: ascending by time, then by ID
    let limit: number | undefined = undefined;

    if (fixtureId && !isNaN(fixtureId)) {
        filter._id = fixtureId;
    } else if (ids) {
        // Validate and parse 'ids' string (e.g., "id1-id2-id3")
        const fixtureIds = (ids as string).split('-').map(idStr => parseInt(idStr)).filter(idNum => !isNaN(idNum));
        if (fixtureIds.length > 0) {
            filter._id = { $in: fixtureIds };
        } else {
            res.status(400).json({ message: 'Invalid format for ids parameter.' }); // Removed return
            return; // Exit handler
        }
    } else if (live) {
        // Live filtering - requires specific status codes
        // Example: TBD, NS, 1H, HT, 2H, ET, BT, P, SUSP, INT, LIVE
        // Note: We use 'BT' here for database queries since that's what the API sends
        const liveStatuses = ['LIVE', '1H', 'HT', '2H', 'ET', 'BT', 'P', 'SUSP', 'INT'];
        filter['fixture.status.short'] = { $in: liveStatuses };
        if (typeof live === 'string' && live !== 'all') {
            // Filter by specific league IDs, e.g., "L39-L61"
            const liveLeagueIds = live.substring(1).split('-').map(idStr => parseInt(idStr)).filter(idNum => !isNaN(idNum));
            if (liveLeagueIds.length > 0) {
                filter['league.id'] = { $in: liveLeagueIds };
            }
        }
        sort = { 'fixture.timestamp': 1 }; // Sort live games by start time
    } else if (date) {
        // Filter by date (match the start of the day)
        if (typeof date === 'string' && dayjs(date, 'YYYY-MM-DD', true).isValid()) {
            let startOfDay: number;
            let endOfDay: number;

            if (timezone && typeof timezone === 'string') {
                // Use the provided timezone to calculate day boundaries
                try {
                    startOfDay = dayjs.tz(date, timezone as string).startOf('day').unix();
                    endOfDay = dayjs.tz(date, timezone as string).endOf('day').unix();
                } catch (error) {
                    console.warn(`Invalid timezone provided: ${timezone}, falling back to UTC`);
                    startOfDay = dayjs(date).startOf('day').unix();
                    endOfDay = dayjs(date).endOf('day').unix();
                }
            } else {
                // Default to UTC if no timezone provided
                startOfDay = dayjs(date).startOf('day').unix();
                endOfDay = dayjs(date).endOf('day').unix();
            }

            filter['fixture.timestamp'] = { $gte: startOfDay, $lte: endOfDay };
        } else {
            res.status(400).json({ message: 'Invalid date format. Use YYYY-MM-DD.' }); // Removed return
            return; // Exit handler
        }
    } else if (leagueId && seasonYear) {
        filter['league.id'] = leagueId;
        filter['league.season'] = seasonYear;
        if (teamIdNum) filter['$or'] = [{ 'teams.home.id': teamIdNum }, { 'teams.away.id': teamIdNum }];
        if (round) filter['league.round'] = round as string;
        if (status) filter['fixture.status.short'] = { $in: (status as string).split('-') };
        if (venueId) filter['fixture.venue.id'] = venueId;
        // Handle 'last' and 'next' for league/season (requires timestamp filtering)
        const nowTimestamp = dayjs().unix();
        if (lastN && !isNaN(lastN)) {
            filter['fixture.timestamp'] = { $lt: nowTimestamp };
            sort = { 'fixture.timestamp': -1, '_id': 1 }; // Sort descending for last N, then by ID
            limit = lastN;
        } else if (nextN && !isNaN(nextN)) {
            filter['fixture.timestamp'] = { $gte: nowTimestamp };
            sort = { 'fixture.timestamp': 1, '_id': 1 }; // Sort ascending for next N, then by ID
            limit = nextN;
        }
    } else if (leagueId && !seasonYear) {
        // For now, return an error when only league is provided without season
        // This will help us isolate the issue
        res.status(400).json({
            message: 'Season parameter is required when league is provided',
            example: '/api/fixtures?league=39&season=2025'
        });
        return;
    } else if (teamIdNum) {
        // Filter by team across all leagues/seasons (potentially large result set)
        filter['$or'] = [{ 'teams.home.id': teamIdNum }, { 'teams.away.id': teamIdNum }];
        const nowTimestamp = dayjs().unix();
        if (lastN && !isNaN(lastN)) {
            filter['fixture.timestamp'] = { $lt: nowTimestamp };
            sort = { 'fixture.timestamp': -1, '_id': 1 }; // Add secondary sort by ID
            limit = lastN;
        } else if (nextN && !isNaN(nextN)) {
            filter['fixture.timestamp'] = { $gte: nowTimestamp };
            sort = { 'fixture.timestamp': 1, '_id': 1 }; // Add secondary sort by ID
            limit = nextN;
        } else {
            // Default sort if no last/next for team query
             sort = { 'fixture.timestamp': -1, '_id': 1 }; // Add secondary sort by ID
        }
    } else if (from && to) {
         if (typeof from === 'string' && dayjs(from, 'YYYY-MM-DD', true).isValid() &&
             typeof to === 'string' && dayjs(to, 'YYYY-MM-DD', true).isValid()) {
            let startRange: number;
            let endRange: number;

            if (timezone && typeof timezone === 'string') {
                // Use the provided timezone to calculate date range boundaries
                try {
                    startRange = dayjs.tz(from, timezone as string).startOf('day').unix();
                    endRange = dayjs.tz(to, timezone as string).endOf('day').unix();
                } catch (error) {
                    console.warn(`Invalid timezone provided: ${timezone}, falling back to UTC`);
                    startRange = dayjs(from).startOf('day').unix();
                    endRange = dayjs(to).endOf('day').unix();
                }
            } else {
                // Default to UTC if no timezone provided
                startRange = dayjs(from).startOf('day').unix();
                endRange = dayjs(to).endOf('day').unix();
            }

            filter['fixture.timestamp'] = { $gte: startRange, $lte: endRange };
         } else {
             res.status(400).json({ message: 'Invalid date format for from/to. Use YYYY-MM-DD.' }); // Removed return
             return; // Exit handler
         }
    }
    // Note: Timezone parameter would affect API fetch, not DB query directly unless timestamps are stored timezone-aware

    // --- Caching Logic ---
    // Complex filtering makes caching difficult. Cache specific common cases.
    let cacheKey: string | null = null;
    let cacheTTL = CACHE_TTL_HOUR; // Default TTL

    if (fixtureId) {
        cacheKey = `${FIXTURES_BASE_CACHE_KEY}id:${fixtureId}`;
        // Check if we're fetching from DB first to determine if it's a live fixture
        const collection = getFixturesCollection();
        const fixture = await collection.findOne({ _id: fixtureId });

        // If it's a live fixture, use 5-second cache
        if (fixture && fixture.fixture && fixture.fixture.status &&
            ['LIVE', '1H', 'HT', '2H', 'ET', 'BT', 'P', 'SUSP', 'INT'].includes(fixture.fixture.status.short)) {
            cacheTTL = CACHE_TTL_5_SECONDS; // Cache live fixtures for 5 seconds
            console.log(`Using 5-second cache for live fixture ${fixtureId}`);
        } else {
            cacheTTL = CACHE_TTL_DAY; // Cache non-live fixtures for 1 day
        }
    } else if (date && !leagueId && !teamIdNum && !live && !status) { // Use teamIdNum
        cacheKey = timezone ? `${FIXTURES_BASE_CACHE_KEY}date:${date}:tz:${timezone}` : `${FIXTURES_BASE_CACHE_KEY}date:${date}`;
        cacheTTL = CACHE_TTL_MINUTE * 10; // Cache day view for 10 mins
    } else if (live === 'all' && !leagueId && !teamIdNum && !status) { // Use teamIdNum
        cacheKey = `${FIXTURES_BASE_CACHE_KEY}live:all`;
        cacheTTL = CACHE_TTL_5_SECONDS; // Cache live view for 5 seconds
    }
    // Add more specific cache keys for common queries (e.g., team next/last) if needed

    try {
        // 1. Check cache
        if (cacheKey) {
            const cachedFixtures = await redisClient.get(cacheKey);
            if (cachedFixtures) {
                console.log(`Serving fixtures from cache (Key: ${cacheKey})`);
                res.status(200).json(JSON.parse(cachedFixtures));
                return;
            }
        }

        // 2. Fetch from DB
        console.log('Fetching fixtures from DB with filter:', JSON.stringify(filter));
        const collection = getFixturesCollection();
        const projection = { lastUpdated: 0 }; // Exclude internal field

        // Handle pagination
        const currentPage = page ? parseInt(page as string) : 1;
        const itemsPerPage = pageSize ? parseInt(pageSize as string) : 15;

        // Only apply pagination if explicitly requested via page parameter AND not using other limiting parameters
        // This ensures backward compatibility and that last/next parameters still work properly
        let isPaginated = page !== undefined && !limit && !fixtureId && !ids;

        // Total count query (only if paginated)
        let totalCount = 0;
        let totalPages = 0;

        if (isPaginated) {
            totalCount = await collection.countDocuments(filter);
            totalPages = Math.ceil(totalCount / itemsPerPage);
        }

        // Build query with appropriate limits
        const query = collection.find(filter, { projection }).sort(sort);

        if (isPaginated) {
            const skip = (currentPage - 1) * itemsPerPage;
            query.skip(skip).limit(itemsPerPage);
            console.log(`Applying pagination: page ${currentPage}, pageSize ${itemsPerPage}, skip ${skip}`);
        } else if (limit) {
            query.limit(limit);
        }

        const fixtures = await query.toArray();

        // 3. Store in cache (only for cacheable queries)
        if (cacheKey && fixtures.length > 0) {
            // Handle single fixture result for ID query
            if (fixtureId && fixtures.length === 1) {
                // Use the same ordered structure for caching
                const fixture = fixtures[0];
                const orderedFixture = {
                    _id: fixture._id,
                    fixture: fixture.fixture,
                    league: fixture.league,
                    teams: fixture.teams,
                    goals: fixture.goals,
                    score: fixture.score,
                    events: fixture.events || [],
                    lineups: fixture.lineups || [],
                    statistics: fixture.statistics || [],
                    players: fixture.players || []
                };
                // Apply status mapping before caching
                const mappedFixture = applyStatusMapping(orderedFixture);
                await redisClient.setex(cacheKey, cacheTTL, JSON.stringify(mappedFixture));
            } else if (isPaginated) {
                // For paginated results, don't cache as different clients might request different pages
                // This avoids issues with page-specific responses being cached and served incorrectly
                console.log('Skipping cache for paginated results');
            } else {
                // For multiple fixtures, apply status mapping before caching
                const mappedFixtures = applyStatusMappingToArray(fixtures);
                await redisClient.setex(cacheKey, cacheTTL, JSON.stringify(mappedFixtures));
            }
            console.log(`Fixtures stored in cache (Key: ${cacheKey})`);
        }

        // Handle single fixture result for ID query
        if (fixtureId && fixtures.length === 0) {
             res.status(404).json({ message: 'Fixture not found' });
             return; // Exit handler
        }
        if (fixtureId && fixtures.length === 1) {
            // Reorder fields for single fixture response
            const fixture = fixtures[0];
            const orderedFixture = {
                _id: fixture._id,
                fixture: fixture.fixture,
                league: fixture.league,
                teams: fixture.teams,
                goals: fixture.goals,
                score: fixture.score,
                events: fixture.events || [],
                lineups: fixture.lineups || [],
                statistics: fixture.statistics || [],
                players: fixture.players || []
            };
            // Apply status mapping
            const mappedFixture = applyStatusMapping(orderedFixture);
            // Remove apiId from response as it's redundant with _id
            // Note: This doesn't change the database, just the response
            res.status(200).json(mappedFixture);
            return; // Exit handler
        }

        // Return array with pagination metadata if paginated
        if (isPaginated) {
            // Add some debugging for pagination issues
            console.log(`Returning paginated response: page ${currentPage}/${totalPages}, ${fixtures.length} fixtures, total: ${totalCount}`);

            // Check if the page is valid
            if (currentPage > totalPages && totalPages > 0) {
                res.status(400).json({
                    error: `Requested page ${currentPage} exceeds available pages (${totalPages})`,
                    pagination: {
                        page: currentPage,
                        pageSize: itemsPerPage,
                        totalPages,
                        totalCount,
                        hasNextPage: false,
                        hasPrevPage: totalPages > 0
                    }
                });
                return;
            }

            // Apply status mapping to fixtures
            const mappedFixtures = applyStatusMappingToArray(fixtures);
            res.status(200).json({
                data: mappedFixtures,
                pagination: {
                    page: currentPage,
                    pageSize: itemsPerPage,
                    totalPages,
                    totalCount,
                    hasNextPage: currentPage < totalPages,
                    hasPrevPage: currentPage > 1
                }
            });
        } else {
            // Return simple array for backward compatibility with existing clients
            // Apply status mapping first
            const mappedFixtures = applyStatusMappingToArray(fixtures);
            res.status(200).json(mappedFixtures);
        }

    } catch (error) {
        console.error('Error fetching fixtures:', error);
        res.status(500).json({ message: 'Failed to fetch fixtures' }); // Removed implicit return
    }
};

// Test route to see if the issue is with the main handler
router.get('/test', (req, res) => {
    res.json({ message: 'Test route works', query: req.query });
});

// GET /api/fixtures - Fetches fixtures with various filters
router.get('/', getFixturesHandler);


// --- Handler for H2H ---
const getH2HHandler: RequestHandler = async (req, res) => {
    const redisClient = getRedisClient();
    const queryParams = req.query;
    const h2hParam = queryParams.h2h as string;

    // Validate required h2h parameter
    if (!h2hParam || !/^\d+-\d+$/.test(h2hParam)) {
        res.status(400).json({ message: 'Missing or invalid required query parameter: h2h (format: teamId1-teamId2)' });
        return;
    }

    // Construct cache key based on h2h param and other optional filters
    // Simple key for now, just using h2h param
    const cacheKey = `${H2H_BASE_CACHE_KEY}${h2hParam}`; // Add other filters to key if caching filtered results

    try {
        // 1. Check cache
        const cachedH2H = await redisClient.get(cacheKey);
        if (cachedH2H) {
            console.log(`Serving H2H from cache (Key: ${cacheKey})`);
            res.status(200).json(JSON.parse(cachedH2H));
            return;
        }

        // 2. Fetch from API service
        console.log(`Fetching H2H from API: ${h2hParam}`);
        // Pass all relevant query params from the request to the service function
        const h2hFixtures = await fetchHeadToHead(queryParams as any); // Cast queryParams, ensure service handles types

        // 3. Store in cache
        // Consider caching even empty results to avoid repeated API calls for non-existent H2H
        await redisClient.setex(cacheKey, CACHE_TTL_5_MINUTES, JSON.stringify(h2hFixtures)); // Corrected TTL constant
        console.log(`H2H stored in cache (Key: ${cacheKey})`);

        res.status(200).json(h2hFixtures);

    } catch (error: any) {
        console.error(`Error fetching H2H for ${h2hParam}:`, error);
        // Handle potential API errors (e.g., invalid team IDs)
        if (error.response?.status === 404 || error.message?.includes('not found')) {
             res.status(404).json({ message: `H2H data not found for ${h2hParam}.` });
        } else {
             res.status(500).json({ message: 'Failed to fetch H2H data' });
        }
    }
};

// GET /api/fixtures/h2h?h2h=X-Y[&...] - Fetches H2H fixtures
router.get('/h2h', getH2HHandler);


// --- Handler for Fixture Statistics ---
const getFixtureStatsHandler: RequestHandler<{ fixtureId: string }> = async (req, res): Promise<void> => {
    const redisClient = getRedisClient();
    const fixtureIdParam = req.params.fixtureId;
    const fixtureId = parseInt(fixtureIdParam);
    const { team, type, half } = req.query; // Optional query params

    if (isNaN(fixtureId)) {
        res.status(400).json({ message: 'Invalid fixture ID provided.' });
        return;
    }

    const teamId = team ? parseInt(team as string) : undefined;
    const typeStr = type as string | undefined;
    const halfStr = half as 'true' | 'false' | undefined;

    // Basic validation for optional params if needed
    if (team && isNaN(teamId as number)) {
        res.status(400).json({ message: 'Invalid team ID.' });
        return; // Exit handler
    }
    if (half && half !== 'true' && half !== 'false') {
        res.status(400).json({ message: 'Invalid half parameter. Use "true" or "false".' });
        return; // Exit handler
    }

    // Build cache key including optional filters
    let cacheKey = `${FIXTURE_STATS_CACHE_KEY}${fixtureId}`;
    if (teamId) cacheKey += `:team:${teamId}`;
    if (typeStr) cacheKey += `:type:${typeStr}`; // Be careful with caching many type variations
    if (halfStr) cacheKey += `:half:${halfStr}`;

    try {
        const cachedData = await redisClient.get(cacheKey);
        if (cachedData) {
            console.log(`Serving fixture stats from cache (Key: ${cacheKey})`);
            res.status(200).json(JSON.parse(cachedData));
            return;
        }

        console.log(`Fetching fixture stats from API for fixture ID: ${fixtureId}`);
        const stats = await fetchFixtureStatistics({ fixture: fixtureId, team: teamId, type: typeStr, half: halfStr });

        await redisClient.setex(cacheKey, CACHE_TTL_5_MINUTES, JSON.stringify(stats)); // Cache for 5 mins
        console.log(`Fixture stats stored in cache (Key: ${cacheKey})`);

        res.status(200).json(stats);

    } catch (error: any) {
        console.error(`Error fetching fixture stats for fixture ID ${fixtureId}:`, error);
        if (error.response?.status === 404 || error.message?.includes('not found')) {
             res.status(404).json({ message: `Statistics not found for fixture ID ${fixtureId}.` });
        } else {
             res.status(500).json({ message: 'Failed to fetch fixture statistics' });
        }
    }
};

// GET /api/fixtures/:fixtureId/statistics
router.get('/:fixtureId/statistics', getFixtureStatsHandler);


// --- Handler for Fixture Events ---
const getFixtureEventsHandler: RequestHandler<{ fixtureId: string }> = async (req, res): Promise<void> => {
    const redisClient = getRedisClient();
    const fixtureIdParam = req.params.fixtureId;
    const fixtureId = parseInt(fixtureIdParam);
    const { team, player, type } = req.query;

    if (isNaN(fixtureId)) {
        res.status(400).json({ message: 'Invalid fixture ID provided.' });
        return;
    }

    const teamId = team ? parseInt(team as string) : undefined;
    const playerId = player ? parseInt(player as string) : undefined;
    const typeStr = type as 'Goal' | 'Card' | 'Subst' | 'Var' | undefined;

    // Validation
    if (team && isNaN(teamId as number)) {
        res.status(400).json({ message: 'Invalid team ID.' });
        return;
    }
    if (player && isNaN(playerId as number)) {
        res.status(400).json({ message: 'Invalid player ID.' });
        return;
    }
    if (type && !['Goal', 'Card', 'Subst', 'Var'].includes(typeStr as string)) {
        res.status(400).json({ message: 'Invalid type parameter.' });
        return;
    }

    let cacheKey = `${FIXTURE_EVENTS_CACHE_KEY}${fixtureId}`;
    if (teamId) cacheKey += `:team:${teamId}`;
    if (playerId) cacheKey += `:player:${playerId}`;
    if (typeStr) cacheKey += `:type:${typeStr}`;

    try {
        const cachedData = await redisClient.get(cacheKey);
        if (cachedData) {
            console.log(`Serving fixture events from cache (Key: ${cacheKey})`);
            res.status(200).json(JSON.parse(cachedData));
            return;
        }

        console.log(`Fetching fixture events from API for fixture ID: ${fixtureId}`);
        const events = await fetchFixtureEvents({ fixture: fixtureId, team: teamId, player: playerId, type: typeStr });

        await redisClient.setex(cacheKey, CACHE_TTL_5_SECONDS, JSON.stringify(events)); // Cache for 5 seconds (updates very frequently during match)
        console.log(`Fixture events stored in cache (Key: ${cacheKey})`);

        res.status(200).json(events);

    } catch (error: any) {
        console.error(`Error fetching fixture events for fixture ID ${fixtureId}:`, error);
        if (error.response?.status === 404 || error.message?.includes('not found')) {
             res.status(404).json({ message: `Events not found for fixture ID ${fixtureId}.` });
        } else {
             res.status(500).json({ message: 'Failed to fetch fixture events' });
        }
    }
};

// GET /api/fixtures/:fixtureId/events
router.get('/:fixtureId/events', getFixtureEventsHandler);


// --- Handler for Fixture Lineups ---
const getFixtureLineupsHandler: RequestHandler<{ fixtureId: string }> = async (req, res): Promise<void> => {
    const redisClient = getRedisClient();
    const fixtureIdParam = req.params.fixtureId;
    const fixtureId = parseInt(fixtureIdParam);
    const { team, player, type } = req.query;

    if (isNaN(fixtureId)) {
        res.status(400).json({ message: 'Invalid fixture ID provided.' });
        return;
    }

    const teamId = team ? parseInt(team as string) : undefined;
    const playerId = player ? parseInt(player as string) : undefined;
    const typeStr = type as string | undefined;

    // Validation
    if (team && isNaN(teamId as number)) {
        res.status(400).json({ message: 'Invalid team ID.' });
        return;
    }
    if (player && isNaN(playerId as number)) {
        res.status(400).json({ message: 'Invalid player ID.' });
        return;
    }

    let cacheKey = `${FIXTURE_LINEUPS_CACHE_KEY}${fixtureId}`;
    if (teamId) cacheKey += `:team:${teamId}`;
    // Add player/type to cache key if needed, though less common for lineups

    try {
        const cachedData = await redisClient.get(cacheKey);
        if (cachedData) {
            console.log(`Serving fixture lineups from cache (Key: ${cacheKey})`);
            res.status(200).json(JSON.parse(cachedData));
            return;
        }

        console.log(`Fetching fixture lineups from API for fixture ID: ${fixtureId}`);
        const lineups = await fetchFixtureLineups({ fixture: fixtureId, team: teamId, player: playerId, type: typeStr });

        await redisClient.setex(cacheKey, CACHE_TTL_5_MINUTES, JSON.stringify(lineups)); // Cache for 5 mins
        console.log(`Fixture lineups stored in cache (Key: ${cacheKey})`);

        res.status(200).json(lineups);

    } catch (error: any) {
        console.error(`Error fetching fixture lineups for fixture ID ${fixtureId}:`, error);
        if (error.response?.status === 404 || error.message?.includes('not found')) {
             res.status(404).json({ message: `Lineups not found for fixture ID ${fixtureId}.` });
        } else {
             res.status(500).json({ message: 'Failed to fetch fixture lineups' });
        }
    }
};

// GET /api/fixtures/:fixtureId/lineups
router.get('/:fixtureId/lineups', getFixtureLineupsHandler);


// --- Handler for Fixture Players Stats ---
const getFixturePlayersHandler: RequestHandler<{ fixtureId: string }> = async (req, res): Promise<void> => {
    const redisClient = getRedisClient();
    const fixtureIdParam = req.params.fixtureId;
    const fixtureId = parseInt(fixtureIdParam);
    const { team } = req.query;

    if (isNaN(fixtureId)) {
        res.status(400).json({ message: 'Invalid fixture ID provided.' });
        return;
    }

    const teamId = team ? parseInt(team as string) : undefined;

    // Validation
    if (team && isNaN(teamId as number)) {
        res.status(400).json({ message: 'Invalid team ID.' });
        return;
    }

    let cacheKey = `${FIXTURE_PLAYERS_CACHE_KEY}${fixtureId}`;
    if (teamId) cacheKey += `:team:${teamId}`;

    try {
        const cachedData = await redisClient.get(cacheKey);
        if (cachedData) {
            console.log(`Serving fixture players stats from cache (Key: ${cacheKey})`);
            res.status(200).json(JSON.parse(cachedData));
            return;
        }

        console.log(`Fetching fixture players stats from API for fixture ID: ${fixtureId}`);
        const playersStats = await fetchFixturePlayers({ fixture: fixtureId, team: teamId });

        await redisClient.setex(cacheKey, CACHE_TTL_15_SECONDS, JSON.stringify(playersStats)); // Cache for 15 seconds
        console.log(`Fixture players stats stored in cache (Key: ${cacheKey})`);

        res.status(200).json(playersStats);

    } catch (error: any) {
        console.error(`Error fetching fixture players stats for fixture ID ${fixtureId}:`, error);
        if (error.response?.status === 404 || error.message?.includes('not found')) {
             res.status(404).json({ message: `Player stats not found for fixture ID ${fixtureId}.` });
        } else {
             res.status(500).json({ message: 'Failed to fetch fixture player stats' });
        }
    }
};

// GET /api/fixtures/:fixtureId/players
router.get('/:fixtureId/players', getFixturePlayersHandler);


// --- Handler for Fixture Rounds ---
const getFixtureRoundsHandler: RequestHandler = async (req, res): Promise<void> => {
    const redisClient = getRedisClient();
    const { league, season, current, dates, timezone } = req.query;

    // Validate required parameters
    if (!league || !season) {
        res.status(400).json({ message: 'Missing required parameters: league and season are required.' });
        return;
    }

    const leagueId = parseInt(league as string);
    const seasonYear = parseInt(season as string);
    const currentRound = current as 'true' | 'false' | undefined;
    const includeDates = dates as 'true' | 'false' | undefined;
    const timezoneStr = timezone as string | undefined;

    // Validate parameter types
    if (isNaN(leagueId) || isNaN(seasonYear)) {
        res.status(400).json({ message: 'Invalid league or season parameter.' });
        return;
    }

    if (currentRound && currentRound !== 'true' && currentRound !== 'false') {
        res.status(400).json({ message: 'Invalid current parameter. Use "true" or "false".' });
        return;
    }

    if (includeDates && includeDates !== 'true' && includeDates !== 'false') {
        res.status(400).json({ message: 'Invalid dates parameter. Use "true" or "false".' });
        return;
    }

    // Build cache key
    let cacheKey = `${FIXTURE_ROUNDS_CACHE_KEY}league:${leagueId}:season:${seasonYear}`;
    if (currentRound) cacheKey += `:current:${currentRound}`;
    if (includeDates) cacheKey += `:dates:${includeDates}`;
    if (timezoneStr) cacheKey += `:timezone:${timezoneStr}`;

    try {
        // Check cache first
        const cachedData = await redisClient.get(cacheKey);
        if (cachedData) {
            console.log(`Serving fixture rounds from cache (Key: ${cacheKey})`);
            res.status(200).json(JSON.parse(cachedData));
            return;
        }

        // Fetch from API
        console.log(`Fetching fixture rounds from API for league: ${leagueId}, season: ${seasonYear}`);
        const rounds = await fetchFixtureRounds({
            league: leagueId,
            season: seasonYear,
            current: currentRound,
            dates: includeDates,
            timezone: timezoneStr
        });

        // Cache the result
        await redisClient.setex(cacheKey, CACHE_TTL_DAY, JSON.stringify(rounds)); // Cache for 1 day
        console.log(`Fixture rounds stored in cache (Key: ${cacheKey})`);

        res.status(200).json(rounds);

    } catch (error: any) {
        console.error(`Error fetching fixture rounds for league ${leagueId}, season ${seasonYear}:`, error);
        if (error.response?.status === 404 || error.message?.includes('not found')) {
            res.status(404).json({ message: `Rounds not found for league ${leagueId}, season ${seasonYear}.` });
        } else {
            res.status(500).json({ message: 'Failed to fetch fixture rounds' });
        }
    }
};

// GET /api/fixtures/rounds
router.get('/rounds', getFixtureRoundsHandler);

export default router;
